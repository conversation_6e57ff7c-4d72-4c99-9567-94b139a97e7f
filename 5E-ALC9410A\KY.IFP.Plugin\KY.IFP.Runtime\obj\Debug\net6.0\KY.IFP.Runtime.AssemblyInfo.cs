//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("长沙开元仪器有限公司")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Copyright © 长沙开元仪器有限公司")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"IFP基础运行库

2021/08/16 刘慧
1、修复含有委托的插件加载时报错的问题。

2021/08/04 刘慧
1、增加Http请求下文抽象类。
2、全局缓存Caches基础信息Basics增加Http上下文抽象类缓存。

2021/07/02 刘慧
1、修复Web前端上传日期时间反序列化后时区相差问题。

2021/05/05 刘慧
1、修复插件加载某些特殊类型被屏蔽的问题。
2、优化事件回调订阅匹配。

2021/04/30 刘慧
1、注入实体增加私有构造方法及构造参数填充。
2、修复某个类型加载出错导致同库其它类型无法加载的问题。

2021/04/29 刘慧
1、增加序列化全局转换，含所有基本类型转换。
2、插件匹配不到空参方法时尝试匹配有参方法。

2021/04/19 刘慧
1、修复继承关系多次加载的问题。
2、修复同版本多实现不能正确加载问题。

2021/04/14 刘慧
1、修复根目录依赖文件不能加载的问题。
2、日志跟踪增加Trace.Fail，可写入错误详情。
3、优化了插件加载的日志输出。

2021/04/13 刘慧
1、日志跟踪增加忽略名单。
2、插件库导入依赖文件夹可设置多个。

2021/04/12 刘慧
1、增加Socket方式调用后台接口。

2021/04/10 刘慧
1、修复无继承关系实例解析出错的问题。

2021/04/02 刘慧
1、更新Nuget包，自动输出.pdb文件。

2021/03/31 刘慧
1、上下文增加一个扩展对象。

2021/03/22 刘慧
1、注入改为自动适配派生类。
2、去掉返回值包装。

2021/03/18 刘慧
1、Json读取扩展增加表达式读取。

2021/03/17 刘慧
1、修复实体不能延时注入的问题。
2、方法注入优化，支持类型限定名方式。

2021/03/16 刘慧
1、完成注入功能，可注入实体和方法。

2021/03/15 刘慧
1、将Trace日志时间由UTC时间改为Local时间。

2021/03/14 刘慧
1、修复日志模块循环输出的问题。
2、增补部分系统.NET框架自带的集约式日志模块名单。
3、增加日志堆栈跟踪忽略特性，方便第三方集约式日志模块使用。
4、优化文本日志写入性能，增加文本日志文件保留数量设置。

2021/03/13 刘慧
1、控制台输出日志时按类型显示不同颜色。

2021/03/12 刘慧
1、插件默认依赖库路径改为'com'目录。

2021/03/11 刘慧
1、插件默认加载api目录下所有dll。
2、nuget包程序增加注释和pdb文件。
	")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("2025.8.25.942")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("5.0.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("IFP")]
[assembly: System.Reflection.AssemblyTitleAttribute("Build In Debug Mode With Machine LAPTOP-BTSO6RJ1")]
[assembly: System.Reflection.AssemblyVersionAttribute("2025.8.25.942")]

// 由 MSBuild WriteCodeFragment 类生成。

