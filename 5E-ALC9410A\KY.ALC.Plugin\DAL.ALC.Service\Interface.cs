
using COM.IFP.Common;

namespace DAL.ALC.Service
{
    /// <summary>
    /// 外部程序集都从这里调用
    /// </summary>
    public class Interface
    {
        #region 系统状态属性

        public bool CPUReady
        {
            get
            {
                if (Global.CPU == null)
                {
                    return false;
                }
                return true;
            }
        }

        public bool PlcIsConnected
        {
            get
            {
                return Global.PlcIsConnected;
            }
        }

        public bool RunStatus
        {
            get
            {
                return Global.RUN_STATUS;
            }
        }

        public bool AutoMode
        {
            get
            {
                // 检查自动运行状态
                if (Global.DataMemory != null && Global.DataMemory.Data.ContainsKey(Constant.ZDFW))
                {
                    var autoRunPoint = Global.DataMemory[Constant.ZDFW];
                    return autoRunPoint.Value != null && Convert.ToBoolean(autoRunPoint.Value);
                }
                return false;
            }
        }

        #endregion

        #region 设备控制方法

        /// <summary>
        /// 执行命令 - 通过SignalR接收的命令
        /// </summary>
        /// <param name="CMD">命令代码</param>
        /// <param name="jo">命令参数</param>
        /// <returns>执行结果</returns>
        public PFActionResult InsertCmd(string CMD, Dictionary<string, object> jo)
        {
            return Global.CPU.InsertCmd(CMD, jo);
        }

        /// <summary>
        /// 获取实时数据推送内容 - 参考SPT项目模式
        /// </summary>
        /// <returns>推送数据字典</returns>
        public Dictionary<string, object> GetData()
        {
            var data = new Dictionary<string, object>();
            
            try
            {
                if (Global.DataMemory != null)
                {
                    // 遍历所有数据点，按照前端需要的key进行分类推送
                    foreach (var point in Global.DataMemory.Data.Values)
                    {
                        if (point?.Value != null)
                        {
                            data.Add(point.Name, point.Value);
                        }
                    }
                }
                
                // 添加系统状态信息
                data["AutoMode"] = AutoMode;
                data["PlcConnected"] = PlcIsConnected;
                data["RunStatus"] = RunStatus;
                data["CpuReady"] = CPUReady;
                data["Timestamp"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            }
            catch (Exception ex)
            {
                LogUtility.ToError("获取实时数据出错", ex);
            }
            
            return data;
        }

        #endregion
    }
}
