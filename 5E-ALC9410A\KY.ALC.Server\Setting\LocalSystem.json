﻿{
  //默认打开的页面，限在wwwroot/pages中实现，登录页
  "LoginHtml": "/pages/alc/login.html",
  //产品注册到中心用到，登陆导航页。
  "MainHtml": "/pages/alc/navMenu.html",
  //心跳包发送时间间隔，单位毫秒
  "HeartbeatPacketTime": 5000,
  //登录过期时间
  "LogoutTime": 600000,
  //是否超时退回登录页
  "TimeoutMode": false,
  "Product": {
    "MainName": "开元智慧燃料物联网平台", //主项目名称
    "Name": "机器人前置处理系统", //子产品名称
    "OrgName": "长沙开元仪器有限公司",
    "HomePage": "/pages/alc/index.html", //首页，登陆后登陆导航页默认显示的子页面。
    "LoginPic": "/img/alc/alc(baner).png", //登录页产品图片
    "MachineCode": "ALC1", //设备编码

    "KYRegisterCode": "", //注册码授权码
    "MachineTypeCode": 0 //存样柜设备类型1650（算法用到）
  },
    "PLC": {
        //  "Type": "Siemens",
        //"IP": "************
        "IP": "**************", // 车间
        //"IP": "************", // 4楼
        //modbus502
        "Port": 102,
        //默认是1
        "Slot": 1

    },

  "InfluxUDP": {
    "IP": "127.0.0.1",
    "Port": 8089
  },
  "InfluxHTTP": {
    "URL": "http://127.0.0.1:8086",
    "User": "admin",
    "Password": "123456",
    "DB": "mydb",
    "RetentionPolicy": "autogen"
  }
}