using COM.IFP.PLC.SiemensS7;
using COM.IFP.PLC.SiemensS7.DataPoint;

namespace DAL.ALC.Service
{
    public static class Global
    {
        #region
        /// <summary>
        /// 后台运行状态,true为运行，否则停止
        /// </summary>
        public static bool RUN_STATUS { set; get; } = false;

        /// <summary>
        /// 轮询间隔没超过最大间隔判定PLC连通
        /// </summary>
        public static bool NO_OVER_TIME { set; get; } = true;

        /// <summary>
        /// PLC的连接状态存储在数据集中，需要单独拿出
        /// </summary>
        static public bool PlcIsConnected
        {
            get
            {
                bool b;
                if (S7Adaptor == null) { b = false; }
                else
                {
                    b = S7Adaptor.GetStatus();//todo
                }
                //如果socket连通且轮询间隔没超，则判定连通
                return (b && NO_OVER_TIME);
                // return false;
            }
        }

        /// <summary>
        /// 轮询点位的延迟时间
        /// </summary>
        public static double PointReadDelay { set; get; } = -1;

        /// <summary>
        /// 轮询运行状态,true为运行，否则停止
        /// </summary>
        public static bool ECHO_POINT_STATUS { set; get; } = false;
 

        /// <summary>
        /// 轮询最小时间间隔单位ms
        /// </summary>
        public static int MIN_INTERVALS = 200;
        /// <summary>
        /// 轮询大时间间隔单位ms
        /// </summary>
        public static int MAX_INTERVALS = 4000;

        /// <summary>
        /// 检测连接状态的时间间隔
        /// </summary>
        public static int CHECK_INTERVALS = 5000;
        #endregion

        public static List<PLCPoint> AllPoint;


        public static Execute Execute { set; get; }
        public static ServiceCPU CPU { set; get; }
        
        /// <summary>
        /// SiemensS7 适配器
        /// </summary>
        public static S7Adaptor S7Adaptor;
        public static DataStorage DataMemory { set; get; }
        public static Lazy<MainDAL> DB_Option { get; set; }


        #region 通用点位操作方法

        /// <summary>
        /// 检查点位值
        /// </summary>
        /// <param name="pointName">点位名称</param>
        /// <param name="expectedValue">期望值</param>
        /// <returns>是否匹配期望值</returns>
        public static bool CheckPointValue(string pointName, object expectedValue)
        {
            try
            {
                if (DataMemory?.Data?.TryGetValue(pointName, out var point) == true)
                {
                    return Equals(point.Value, expectedValue);
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 写入点位值
        /// </summary>
        /// <param name="pointName">点位名称</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否写入成功</returns>
        public static bool WritePointValue(string pointName, object value)
        {
            try
            {
                if (DataMemory?.Data?.TryGetValue(pointName, out var point) == true)
                {
                    DataMemory.Write(point, value);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
