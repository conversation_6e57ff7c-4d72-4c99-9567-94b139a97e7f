
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using ORM.ALC;
using COM.IFP.PLC.SiemensS7.DataPoint;

namespace DAL.ALC.Service
{
    public class ServiceMain
    {
        public static ServiceMain alcServiceMain = new ServiceMain();

        /// <summary>
        /// 用于比对自动重连线程是否abort，是的话重启线程。
        /// </summary>
        uint lastAccumulator = 0;
        /// <summary>
        /// 用于比对自动重连线程是否abort，是的话重启线程。
        /// </summary>
        uint accumulator = 1;

        private ServiceMain()
        {
        }
        #region 
        void PointRefresh(PLCPoint point)
        {
            LogUtility.ToNotice($"点位值变化{point.Name}[{point.Block}.{point.ByteOffset}.{point.BitOffset}]={point.Value}");

        }

        void PointWrite(PLCPoint point, object val)
        {
            LogUtility.ToNotice($"写点位值{point.Name}={val}");
        }
        #endregion
        /// <summary>
        /// 轮询PLC点位
        /// </summary>
        private void EchoPoint()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                {
                    //打印线程号
                    string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                    string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                    sr.WriteLine(line);
                }
#endif
                Global.NO_OVER_TIME = true;
                //NO_SOCKET_EXCEPTION = true;
                //等待socket连通
                if (Global.PlcIsConnected == false)
                {
                    Thread.Sleep(2000);
                }
                
                LogUtility.ToNotice("1#PLC点位开始轮询", 0);
                //标记开始轮询
                Global.ECHO_POINT_STATUS = true;
                //重启服务后防止时间过久超时
                Global.DataMemory.LastMoment = DateTime.Now;
                //记录一次轮询结束的时刻
                DateTime lastTime;
                //重新统计采集系统心跳

                while (true)
                {
                    if (!Global.RUN_STATUS ||
                    !Global.PlcIsConnected)
                    {
                        LogUtility.ToNotice($"AssGlobal.RUN_STATUS={Global.RUN_STATUS};AssGlobal.PlcIsConnected={Global.PlcIsConnected};", 0);

                        Global.PointReadDelay = -1;

                        //控制其他子线程return;
                        Global.RUN_STATUS = false;
                        LogUtility.ToNotice("后台服务关闭", 0);

                        //标记关闭轮询
                        Global.ECHO_POINT_STATUS = false;
                        LogUtility.ToNotice("1#PLC点位轮询被关闭", 0);
                        return;
                    }
                    lastTime = Global.DataMemory.LastMoment;
                    //防止抛出异常后退出循环线程
                    try
                    {
                        Global.DataMemory.RefreshAll();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError($"1#PLC点位轮询，更新点位异常。", 0, e);
                    }
                    try
                    {
                        double delay = (DateTime.Now - lastTime).TotalMilliseconds;
                        //轮询间隔超过最大间隔辅助判定PLC断开连通
                        if (Global.RUN_STATUS == true
                        && delay > Global.MAX_INTERVALS)
                        {
                            Global.NO_OVER_TIME = false;
                            Global.PointReadDelay = delay;
                            LogUtility.ToNotice($"轮询间隔超时({Global.PointReadDelay}ms)，PLC断开连接", 0);
                        }

                        //限制最少时间间隔
                        if (delay < Global.MIN_INTERVALS)
                        {
                            int t = Global.MIN_INTERVALS - (int)delay;
                            Thread.Sleep(t);
                            Global.PointReadDelay = (DateTime.Now - lastTime).TotalMilliseconds;
                        }
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError($"1#PLC点位轮询，时间统计异常。\r\n{e}", 0);
                    }

#if debug
                    Task.Run(() =>
                    {
                        LoggerHelper.Info($"【RefreshAll】{AssGlobal.PointReadDelay}ms");
                    });
#endif
                }
            }, TaskCreationOptions.LongRunning);
        }

        #region

        public void ServiceStart()
        {
            try
            {
                //0、写入开启标记，后面都是异步循环方法，如果不先置位，
                //那么后面的方法可能会判断RunStatus==false从而停止。
                Global.RUN_STATUS = true;
                LogUtility.ToNotice("后台服务启动成功", 0);

                //1、开始轮询点位
                EchoPoint();
                //2、更新下位机参数
                //Params2PLC();
                //3、开启报警监控
                EchoAlramTask();
               
                //开启获取数据服务
                EchoGetData();
                //Global.UC.EchoAutoGasTakeBack();
                //启动三个服务
               
            }
            catch (Exception e)
            {
                Global.RUN_STATUS = false;
                throw;
            }
        }

        /// <summary>
        /// 自动重连线程守护
        /// </summary>
        public void AutoRestart()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                {
                    //打印线程号
                    string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                    string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                    sr.WriteLine(line);
                }
#endif
                LogUtility.ToNotice("启动自动重启服务功能", 0);
                while (true)
                {
                    try
                    {
                        //如果服务关闭，其他子线程也要结束，不然因为异步，启动又被没结束的子线程关闭服务
                        //已经改成只有轮询线程内能设置标志关闭其他子线程。
                        if (Global.RUN_STATUS == false &&
                        Global.ECHO_POINT_STATUS == false)
                        {
                            try
                            {
                                                Global.S7Adaptor.Build();

                if (Global.S7Adaptor.GetStatus() == false)
                                {
                                    continue;
                                }
                                try
                                {
                                    // 减去触发任务的委托，防止重复订阅
                                    if (Global.DataMemory != null)
                                    {
                                        //由下位机点位上升沿跳变触发的功能块，将触发任务委托到点位值更新事件
                                        Global.DataMemory.ONPointRefresh -= PointRefresh;
                                    }

                                    ServiceStart();
                                    LogUtility.ToNotice("自动重启服务成功", 0);
                                }
                                catch (Exception e)
                                {
                                    LogUtility.ToError("自动重启服务失败", 0, e);
                                }

                            }
                            catch (Exception e)
                            {
                                LogUtility.ToNotice($"自动重启服务失败。{e}", 0);
                            }
                        }
                    }
                    catch { }
                    finally
                    {
                        ++accumulator;
                        Thread.Sleep(Global.CHECK_INTERVALS);
                    }
                }
            }, TaskCreationOptions.LongRunning);
        }
        /// <summary>
        /// 自动重连线程守护
        /// </summary>
        public void AutoRestartGuard()
        {
            Task.Factory.StartNew(() =>
            {
#if LookThreadNum
                Task.Run(() =>
                {
                    using (System.IO.StreamWriter sr = new System.IO.StreamWriter("D:/ThreadNum.txt", true))
                    {
                        //打印线程号
                        string sTmp = "0x" + Thread.CurrentThread.ManagedThreadId.ToString("X");
                        string line = $"{DateTime.Now.ToString()}#{System.Reflection.MethodBase.GetCurrentMethod().Name}#{sTmp};";
                        sr.WriteLine(line);
                    }
                });
#endif
                LogUtility.ToNotice("启动自动重连服务守护线程", 0);
                while (true)
                {
                    if (lastAccumulator != accumulator)
                    {
                        lastAccumulator = accumulator;
                    }
                    else
                    {
                        AutoRestart();
                    }
                    Thread.Sleep(Global.CHECK_INTERVALS * 4);
                }
            }, TaskCreationOptions.LongRunning);

        }
        #endregion  


        //static List<Point> AllPoint;
        /// <summary>
        /// 初始化操作
        /// </summary>
        public void Init()
        {

            #region 处理PLC通讯初始化
            try
            {
                var config = COM.IFP.Common.Config.LocalSystem.GetProperty("PLC");

                string ip = config.GetProperty("IP").GetString();
                int port = config.GetProperty("Port").GetInt32();
                int slot = config.GetProperty("Slot").GetInt32();

                var ada = new COM.IFP.PLC.SiemensS7.S7Adaptor(ip, port, slot);
                Global.S7Adaptor = ada;
                ada.Build();
            }
            catch (Exception e)
            {
                LogUtility.ToError("PLC连接失败", 0, e);
            }
            #endregion

            #region 数据库操作对象初始化
            //作为静态变量类的成员变量可能在加载程序集前就初始化导致错误，故手动赋值
            //Entity.Create方法类的成员变量初始化使用也会有异常
            Global.DB_Option = COM.IFP.Common.Entity.Create<MainDAL>();
            #endregion

            #region 初始化数据缓存器
            try
            {
                List<PLCPoint> source = Global.DB_Option.Value.AllPLCPoints();
                if (source.Count < 1)
                {
                    LogUtility.ToError("数据缓存器构建失败，没有找到点表源数据", 0);
                    return;
                }

                Global.DataMemory = new DataStorage();
                Global.DataMemory.Import(source);
                Global.DataMemory.Inject(Global.S7Adaptor);

                Global.DataMemory.ONPointRefresh += PointRefresh;
                Global.DataMemory.ONPointWrite += PointWrite;

                LogUtility.ToNotice($"初始化步骤3，数据缓存器构建成功，点表长度{Global.DataMemory.Data.Count}", 0);
            }
            catch (Exception e)
            {
                LogUtility.ToError("初始化步骤3，数据缓存器构建失败", 0, e);
                return;
            }

            #endregion

            #region LowerMachine

            #endregion

           

            #region CPU
            Global.Execute = new Execute();
            Global.Execute.Inject(Global.DataMemory);

            Global.CPU = new ServiceCPU();
            Global.CPU.Inject(Global.Execute, Global.DataMemory);

            LogUtility.ToNotice("初始化步骤4，服务类初始化成功", 0);
            #endregion

            #region 业务服务初始化
            
            #endregion

            #region 初始化udp发送

            #endregion


            #region 启动服务及服务自动重
            try
            {
                ServiceStart();
                Thread.Sleep(500);
                AutoRestart();
                AutoRestartGuard();
            }
            catch (Exception e)
            {
                LogUtility.ToError("后台服务启动失败", 0, e);
            }
            #endregion
        }


        #region 自动功能线程
        /// <summary>
        /// 自动报警监控
        /// </summary>
        private void EchoAlramTask()
        {
            Task.Factory.StartNew(() =>
            {
                LogUtility.ToNotice("启动自动报警监控", 0);
                while (true)
                {
                    try
                    {
                        if (!Global.RUN_STATUS || !Global.PlcIsConnected)
                        {
                            LogUtility.ToNotice("自动报警监控被关闭", 0);
                            return;
                        }

                       // Global.CPU.AutoAlarm();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError("自动报警监控异常", 0, e);
                    }
                    finally
                    {
                        Thread.Sleep(2000); // 每2秒检查一次报警
                    }
                }
            }, TaskCreationOptions.LongRunning);
        }
        /// <summary>
        /// 获取数据服务
        /// </summary>
        private void EchoGetData()
        {
            Task.Factory.StartNew(() =>
            {
                LogUtility.ToNotice("启动获取数据服务", 0);
                while (Global.RUN_STATUS)
                {
                    try
                    {
                        // 获取数据逻辑
                       // Global.CPU.AutoGetData();
                    }
                    catch (Exception e)
                    {
                        LogUtility.ToError("获取数据服务异常", 0, e);
                    }
                    finally
                    {
                        Thread.Sleep(1000); // 每秒获取一次数据
                    }
                }
                LogUtility.ToNotice("获取数据服务停止", 0);
            }, TaskCreationOptions.LongRunning);
        }


        #endregion
        
        /// <summary>
        /// 由框架调用启动本服务，配置Launch.json
        /// </summary>
        public static void Start()
        {
            Task.Run(() =>
            {
                alcServiceMain.Init();
                //ServiceDiagram.Init();
            }
            );
        }
    }
}
