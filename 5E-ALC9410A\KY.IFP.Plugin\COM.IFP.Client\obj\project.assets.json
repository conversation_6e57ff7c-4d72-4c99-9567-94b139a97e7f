{"version": 3, "targets": {"net6.0": {"Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/6.0.2": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.1"}, "compile": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.PerformanceCounter/6.0.2": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.2"}, "compile": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/6.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/6.0.1": {"type": "package", "dependencies": {"System.Windows.Extensions": "6.0.0"}, "compile": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Windows.Extensions/6.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "6.0.0"}, "compile": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "COM.IFP.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"COM.IFP.Log": "1.0.0", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "6.0.0"}, "compile": {"bin/placeholder/COM.IFP.Common.dll": {}}, "runtime": {"bin/placeholder/COM.IFP.Common.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "COM.IFP.ComputerInfo/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.2"}, "compile": {"bin/placeholder/COM.IFP.ComputerInfo.dll": {}}, "runtime": {"bin/placeholder/COM.IFP.ComputerInfo.dll": {}}}, "COM.IFP.Log/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/COM.IFP.Log.dll": {}}, "runtime": {"bin/placeholder/COM.IFP.Log.dll": {}}}, "COM.IFP.UDPHelper/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/COM.IFP.UDPHelper.dll": {}}, "runtime": {"bin/placeholder/COM.IFP.UDPHelper.dll": {}}}}}, "libraries": {"Microsoft.Win32.SystemEvents/6.0.0": {"sha512": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "type": "package", "path": "microsoft.win32.systemevents/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.6.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.1/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.Configuration.ConfigurationManager/6.0.2": {"sha512": "LtqgY79MRntWIM0AeQf4uj1mGyqpVhR7O9N1+UODQu/EGQwRTERqMqpYTeedE7VTK2ngoCtxzAaTn9gRDa+6Qw==", "type": "package", "path": "system.configuration.configurationmanager/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/net461/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.dll", "runtimes/win/lib/net461/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.6.0.2.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/6.0.2": {"sha512": "4SLKj8/YJ+uA7YxmKmTfk6Pnn89nxIDipfNaApe1E3I2SI+f1+INj75ysTAtKwIWj7yQK8iiUjlRcJfiINwFUQ==", "type": "package", "path": "system.diagnostics.performancecounter/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.PerformanceCounter.dll", "lib/net461/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll", "lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.6.0.2.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/6.0.0": {"sha512": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "type": "package", "path": "system.drawing.common/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/net461/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/netcoreapp3.1/System.Drawing.Common.dll", "lib/netcoreapp3.1/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/unix/lib/net6.0/System.Drawing.Common.dll", "runtimes/unix/lib/net6.0/System.Drawing.Common.xml", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.1/System.Drawing.Common.xml", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.1/System.Drawing.Common.xml", "system.drawing.common.6.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/6.0.0": {"sha512": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "type": "package", "path": "system.security.cryptography.protecteddata/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/6.0.1": {"sha512": "QufGNXopGXxdXbkDn87hta4ajdNKNI3a1+HoJbKkmBbMtYPhEgEJKSiPZHGjI0zQpgZ7gi5L0gSV3PeewKRtew==", "type": "package", "path": "system.security.permissions/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/net5.0/System.Security.Permissions.dll", "lib/net5.0/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/netcoreapp3.1/System.Security.Permissions.dll", "lib/netcoreapp3.1/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "runtimes/win/lib/net461/System.Security.Permissions.dll", "runtimes/win/lib/net461/System.Security.Permissions.xml", "system.security.permissions.6.0.1.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Windows.Extensions/6.0.0": {"sha512": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "type": "package", "path": "system.windows.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/netcoreapp3.1/System.Windows.Extensions.dll", "lib/netcoreapp3.1/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.1/System.Windows.Extensions.xml", "system.windows.extensions.6.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}, "COM.IFP.Common/1.0.0": {"type": "project", "path": "../COM.IFP.Common/COM.IFP.Common.csproj", "msbuildProject": "../COM.IFP.Common/COM.IFP.Common.csproj"}, "COM.IFP.ComputerInfo/1.0.0": {"type": "project", "path": "../COM.IFP.ComputerInfo/COM.IFP.ComputerInfo.csproj", "msbuildProject": "../COM.IFP.ComputerInfo/COM.IFP.ComputerInfo.csproj"}, "COM.IFP.Log/1.0.0": {"type": "project", "path": "../COM.IFP.Log/COM.IFP.Log.csproj", "msbuildProject": "../COM.IFP.Log/COM.IFP.Log.csproj"}, "COM.IFP.UDPHelper/1.0.0": {"type": "project", "path": "../COM.IFP.UDPHelper/COM.IFP.UDPHelper.csproj", "msbuildProject": "../COM.IFP.UDPHelper/COM.IFP.UDPHelper.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["COM.IFP.Common >= 1.0.0", "COM.IFP.ComputerInfo >= 1.0.0", "COM.IFP.Log >= 1.0.0", "COM.IFP.UDPHelper >= 1.0.0", "Newtonsoft.Json >= 13.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj", "projectName": "COM.IFP.Client", "projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\COM.IFP.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Common\\COM.IFP.Common.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.Log\\COM.IFP.Log.csproj"}, "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj": {"projectPath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.UDPHelper\\COM.IFP.UDPHelper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1900", "level": "Warning", "warningLevel": 1, "message": "获取包漏洞数据时出错: 无法加载源 https://api.nuget.org/v3/index.json 的服务索引。"}]}