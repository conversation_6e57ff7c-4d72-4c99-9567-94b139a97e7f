using COM.IFP.Common;
using System.Reflection;

namespace DAL.ALC.Service
{
    public class ServiceCPU
    {
        Execute _execute = null;
        DataStorage _dataStorage = null;

        public void Inject(Execute execute, DataStorage dataStorage)
        {
            _execute = execute;
            _dataStorage = dataStorage;
        }

        /// <summary>
        /// 执行命令 - 通过反射的方法，映射到相应的类里去操作
        /// </summary>
        public PFActionResult InsertCmd(string CMD, Dictionary<string, object> param)
        {
            MethodInfo method = (typeof(ServiceCPU)).GetMethod(CMD, BindingFlags.NonPublic | BindingFlags.Instance, null, new Type[] { typeof(Dictionary<string, object>) }, null);
            PFActionResult result = new PFActionResult();
            result.success = false;
            
            if (method == null)
            {
                result.success = false;
                string msg = "未找到命令[" + CMD + "]";
                result.msg = msg;
                LogUtility.ToError(msg, 2);
                return result;
            }

            object[] parameters = new object[] { param };
            try
            {
                result.msg = (string)method.Invoke(this, parameters);
                result.success = true;
                return result;
            }
            catch (Exception exp)
            {
                result.success = false;
                LogUtility.ToError("", 2, exp);
                string str = exp.Message;

                // 返回给前台的错误信息不带Exception
                if (str.Split(' ')[0] == "Exception") str = string.Empty;
                if (exp.InnerException != null)
                {
                    string tmp = exp.InnerException.Message;
                    if (tmp.Split(' ')[0] == "Exception") tmp = string.Empty;
                    str += ("\r\n" + tmp);
                }
                result.msg = $"执行失败，{str}";
                return result;
            }
        }

        /// <summary>
        /// 机器人速度设置操作
        /// </summary>
        /// <param name="content">命令参数，包含speed参数</param>
        /// <returns>执行结果消息</returns>
        string ROBOT_SPEED_SET(Dictionary<string, object> content)
        {
            try
            {
                if (content.TryGetValue("speed", out var speedObj) && speedObj is int speed)
                {
                    if (speed < 1 || speed > 100)
                    {
                        throw new Exception("速度值应在1-100之间");
                    }
                    _execute.RobotSetSpeed(speed);
                    string msg = $"机器人速度设置操作执行成功: {speed}%";
                    LogUtility.ToNotice(msg, 2);
                    return msg;
                }
                throw new Exception("缺少speed参数或参数类型错误");
            }
            catch (Exception ex)
            {
                throw new Exception($"机器人速度设置操作失败: {ex.Message}");
            }
        }


        /// <summary>
        /// 通用维护模块命令执行方法
        /// </summary>
        /// <param name="content"></param>
        /// <returns>执行结果消息</returns>
        string ExecuteMaintenanceCommand(Dictionary<string, object> content)
        {
            try
            {
                // 从参数中提取cmd（PLC常量）
                if (!content.TryGetValue("cmd", out var cmdObj) || cmdObj == null)
                {
                    throw new Exception("缺少cmd参数");
                }
                if (!content.TryGetValue("value", out var Value) || Value == null)
                {
                    throw new Exception("缺少value参数");
                }
                string plcConstant = cmdObj.ToString();
                var value = Convert.ToBoolean(Value);
                // 执行安全检查
                _execute.PleaseStopAuto();
                // 直接写入PLC点位
                _execute.WritePlcPoint(plcConstant, value);
                
                string msg = $"维护命令[{plcConstant}]执行成功";
                LogUtility.ToNotice(msg, 2);
                return msg;
            }
            catch (Exception ex)
            {
                throw new Exception($"维护命令执行失败: {ex.Message}");
            }
        }
    }
}
