{"version": 2, "dgSpecHash": "YD8AGnahuMc=", "success": true, "projectFilePath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\COM.IFP.ComputerInfo\\COM.IFP.ComputerInfo.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.2\\system.configuration.configurationmanager.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\6.0.2\\system.diagnostics.performancecounter.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\6.0.1\\system.security.permissions.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "warningLevel": 1, "message": "获取包漏洞数据时出错: 无法加载源 https://api.nuget.org/v3/index.json 的服务索引。"}]}