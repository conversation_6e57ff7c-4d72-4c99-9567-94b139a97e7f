{"version": 2, "dgSpecHash": "3SHXFxOCqOo=", "success": true, "projectFilePath": "E:\\svn_repo\\IFP_Product\\5E-RLC_ALC\\5E-ALC\\5E-ALC9410A\\KY.IFP.Plugin\\DAL.IFP.Filter\\DAL.IFP.Filter.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "warningLevel": 1, "message": "获取包漏洞数据时出错: 无法加载源 https://api.nuget.org/v3/index.json 的服务索引。"}]}