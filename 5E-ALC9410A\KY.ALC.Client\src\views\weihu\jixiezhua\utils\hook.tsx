import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useGlobalSignalR } from '@/composables/useSignalR'
import type { MaintenanceHookReturn } from '../../common/maintenance-template'
import {
  sendMaintenanceCommand,
  withLoadingState
} from '../../common/maintenance-standard'
import { createFunctionalProcessor, type StatusMappingTable } from '../../common/realtime-processor'



/**
 * 机械夹爪维护Hook
 */
export function useJixiezhuaMaintenance(): MaintenanceHookReturn {
  const signalR = useGlobalSignalR()
  const isLoading = ref(false)

  // ==================== 业务差异化部分 ====================

  // 1. 命令映射配置（机械夹爪专用）
  const commandMap: Record<string, string | { command: string; value?: boolean }> = {
    // 机器人控制命令
    '机器人-启动': { command: 'Manu_机器人启动', value: true },
    '机器人-停止': { command: 'Manu_机器人停止', value: true },
    '机器人-复位': { command: 'Manu_机器人复位', value: true },
    '机器人设置速度-设置': { command: 'Manu_机器人设置速度', value: true },
    '机器人程序初始化-初始化': { command: 'Manu_机器人程序初始化', value: true },
    '机器人状态机重置-重置': { command: 'Manu_机器人状态机重置', value: true },
    '机器人任务读取-读取': { command: 'Manu_机器人任务读取', value: true },

    // 夹爪控制命令 - 伸出/缩回互斥操作
    '夹爪气缸-伸出': { command: 'Manu_夹爪气缸', value: true },
    '夹爪气缸-缩回': { command: 'Manu_夹爪气缸', value: true }
  }
  
  // 2. 响应式状态初始化（机械夹爪专用）
  const buttonStates = reactive<import('../../common/maintenance-template').ButtonStates>({
    '机器人': { '启动': false, '停止': false, '复位': false },
    '机器人设置速度': { '设置': false },
    '机器人程序初始化': { '初始化': false },
    '机器人状态机重置': { '重置': false },
    '机器人任务读取': { '读取': false },
    '夹爪气缸': { '伸出': false, '缩回': false }
  })

  const deviceStatus = reactive<import('../../common/maintenance-template').DeviceStatus>({
    '夹爪气缸': {
      '伸出到位': 'error',
      '缩回到位': 'error'
    }
  })

  // 3. 统一的SignalR命令发送函数
  const sendControlCommand = async (deviceType: string, action: string, params: any = {}): Promise<void> => {
    await withLoadingState(isLoading, async () => {
      const success = await sendMaintenanceCommand(signalR, commandMap, deviceType, action, params)

      if (success) {
        // 更新按钮状态 - 机械夹爪特殊逻辑
        if (buttonStates[deviceType]) {
          // 夹爪气缸：伸出和缩回互斥
          if (deviceType === '夹爪气缸') {
            Object.keys(buttonStates[deviceType]).forEach(key => {
              buttonStates[deviceType][key] = false
            })
            buttonStates[deviceType][action] = true

            // 更新状态指示器
            if (deviceStatus[deviceType]) {
              Object.keys(deviceStatus[deviceType]).forEach(key => {
                deviceStatus[deviceType][key] = 'error'
              })
              if (action === '伸出') {
                deviceStatus[deviceType]['伸出到位'] = 'success'
              } else if (action === '缩回') {
                deviceStatus[deviceType]['缩回到位'] = 'success'
              }
            }
          }
          // 机器人控制：启动/停止/复位互斥（单控切换接组）
          // 按照文档要求：点击任一按钮时，该按钮变绿色，其他按钮变灰色
          else if (deviceType === '机器人') {
            // 先清除所有按钮状态（变为灰色）
            Object.keys(buttonStates[deviceType]).forEach(key => {
              buttonStates[deviceType][key] = false
            })
            // 只激活当前操作的按钮（变为绿色）
            buttonStates[deviceType][action] = true
          }
          // 其他单一操作设备：简单切换状态
          else {
            buttonStates[deviceType][action] = !buttonStates[deviceType][action]
          }
        }
      }
    })
  }
  
  // 4. 统一的按钮点击处理函数
  const handleButtonClick = async (deviceType: string, action: string): Promise<void> => {
    try {
      await sendControlCommand(deviceType, action)
    } catch (error) {
      console.error('机械夹爪按钮操作失败:', error)
    }
  }

  // 状态映射配置表 - 声明式配置，易于维护和扩展
  const statusMappings: StatusMappingTable = {
    // 机器人控制 - 互斥操作
    '机器人启动': {
      deviceType: '机器人',
      action: '启动',
      strategy: 'exclusive',
      excludes: ['停止']
    },
    '机器人停止': {
      deviceType: '机器人',
      action: '停止',
      strategy: 'exclusive',
      excludes: ['启动']
    },
    '机器人复位': {
      deviceType: '机器人',
      action: '复位',
      strategy: 'toggle'
    },

    // 机器人功能 - 简单切换
    '机器人程序初始化': {
      deviceType: '机器人程序初始化',
      action: '初始化',
      strategy: 'toggle'
    },
    '机器人状态机重置': {
      deviceType: '机器人状态机重置',
      action: '重置',
      strategy: 'toggle'
    },
    '机器人任务读取': {
      deviceType: '机器人任务读取',
      action: '读取',
      strategy: 'toggle'
    },
    '机器人设置速度': {
      deviceType: '机器人设置速度',
      action: '设置',
      strategy: 'toggle'
    },

    // 夹爪气缸 - 互斥操作 + 状态指示器
    '夹爪气缸伸出': {
      deviceType: '夹爪气缸',
      action: '伸出',
      strategy: 'custom',
      customHandler: (value, buttonStates, deviceStatus) => {
        // 处理按钮状态（排除到位状态的干扰）
        if (buttonStates['夹爪气缸']) {
          buttonStates['夹爪气缸']['伸出'] = value
          if (value) buttonStates['夹爪气缸']['缩回'] = false
        }
      }
    },
    '夹爪气缸缩回': {
      deviceType: '夹爪气缸',
      action: '缩回',
      strategy: 'custom',
      customHandler: (value, buttonStates, deviceStatus) => {
        // 处理按钮状态（排除到位状态的干扰）
        if (buttonStates['夹爪气缸']) {
          buttonStates['夹爪气缸']['缩回'] = value
          if (value) buttonStates['夹爪气缸']['伸出'] = false
        }
      }
    },

    // 夹爪气缸到位状态 - 状态指示器
    '夹爪气缸伸出到位': {
      deviceType: '夹爪气缸',
      action: '伸出到位',
      strategy: 'custom',
      customHandler: (value, buttonStates, deviceStatus) => {
        if (deviceStatus['夹爪气缸']) {
          const newStatus = value ? 'success' : 'error'
          console.log('夹爪气缸伸出到位状态更新:', value, '->', newStatus)
          deviceStatus['夹爪气缸']['伸出到位'] = newStatus
        }
      }
    },
    '夹爪气缸缩回到位': {
      deviceType: '夹爪气缸',
      action: '缩回到位',
      strategy: 'custom',
      customHandler: (value, buttonStates, deviceStatus) => {
        if (deviceStatus['夹爪气缸']) {
          const newStatus = value ? 'success' : 'error'
          console.log('夹爪气缸缩回到位状态更新:', value, '->', newStatus)
          deviceStatus['夹爪气缸']['缩回到位'] = newStatus
        }
      }
    }
  }

  // 创建现代化的实时数据处理器
  const handleRealtimeData = createFunctionalProcessor(
    statusMappings,
    buttonStates,
    deviceStatus,
    '机械夹爪'
  )
  
  // 6. 统一的生命周期处理
  let cleanupSignalR: (() => void) | null = null

  onMounted(() => {
    if (signalR.isConnected.value) {
      signalR.on('Message', handleRealtimeData)
      cleanupSignalR = () => signalR.off('Message', handleRealtimeData)
    }
  })

  onUnmounted(() => {
    if (cleanupSignalR) {
      cleanupSignalR()
    }
  })
  
  // 7. 统一的返回接口
  return {
    buttonStates,
    deviceStatus,
    isLoading,
    handleButtonClick,
    sendControlCommand
  }
}
